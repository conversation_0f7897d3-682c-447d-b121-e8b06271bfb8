import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * GET /api/user/account-type
 * 
 * Determina il tipo di account dell'utente corrente:
 * - OAuth: utente registrato tramite provider esterni (Google, Apple, etc.)
 * - Credentials: utente registrato con email/password
 * 
 * Criteri di determinazione:
 * 1. Presenza di record nella tabella Account con provider OAuth
 * 2. Campo password: null per OAuth puri, string per credentials
 * 3. Combinazioni miste: utenti che hanno sia OAuth che password
 */
export async function GET() {
  try {
    // Verifica autenticazione
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }

    // Recupera informazioni utente e account collegati
    const userWithAccounts = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        password: true,
        emailVerified: true,
        accounts: {
          select: {
            provider: true,
            type: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    });

    if (!userWithAccounts) {
      return NextResponse.json(
        { error: 'Utente non trovato' },
        { status: 404 }
      );
    }

    // Analizza i provider OAuth collegati
    const oauthProviders = userWithAccounts.accounts
      .filter(account => account.type === 'oauth')
      .map(account => account.provider);

    // Determina il tipo di account
    const hasPassword = userWithAccounts.password !== null;
    const hasOAuthProviders = oauthProviders.length > 0;

    // Logica di determinazione del tipo account
    let accountType: 'oauth' | 'credentials' | 'mixed';
    let canChangePassword: boolean;

    if (hasOAuthProviders && !hasPassword) {
      // Utente OAuth puro (es. registrato solo con Google)
      accountType = 'oauth';
      canChangePassword = false;
    } else if (!hasOAuthProviders && hasPassword) {
      // Utente credentials puro (registrato con email/password)
      accountType = 'credentials';
      canChangePassword = true;
    } else if (hasOAuthProviders && hasPassword) {
      // Utente misto (ha sia OAuth che password)
      accountType = 'mixed';
      canChangePassword = true;
    } else {
      // Caso edge: nessun provider e nessuna password (non dovrebbe accadere)
      accountType = 'credentials';
      canChangePassword = false;
    }

    // Determina il provider primario (primo registrato)
    const primaryProvider = oauthProviders.length > 0 ? oauthProviders[0] : null;

    // Costruisci la risposta
    const response = {
      accountType,
      isOAuthUser: hasOAuthProviders,
      hasPassword,
      canChangePassword,
      providers: oauthProviders,
      primaryProvider,
      emailVerified: userWithAccounts.emailVerified !== null,
      metadata: {
        totalProviders: oauthProviders.length,
        registrationMethod: primaryProvider || 'credentials',
        securityLevel: hasPassword && hasOAuthProviders ? 'high' : 'standard'
      }
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Errore nel recupero tipo account:', error);
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}
