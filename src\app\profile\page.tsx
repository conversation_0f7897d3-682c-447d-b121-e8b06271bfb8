'use client';

import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { 
  User, 
  Mail, 
  Shield, 
  Calendar, 
  MapPin, 
  Settings,
  CheckCircle,
  AlertCircle,
  Lock
} from 'lucide-react';
import { UserRole, UserRoleLabels, UserRoleDescriptions } from '@/types/profile';
import ChangePasswordForm from '@/components/ChangePasswordForm';
import { useToast } from '@/hooks/useToast';

interface AccountTypeData {
  accountType: 'oauth' | 'credentials' | 'mixed';
  isOAuthUser: boolean;
  hasPassword: boolean;
  canChangePassword: boolean;
  providers: string[];
  primaryProvider: string | null;
  emailVerified: boolean;
  metadata: {
    totalProviders: number;
    registrationMethod: string;
    securityLevel: 'high' | 'standard';
  };
}

interface UserStats {
  tripsCount: number;
  joinDate: string;
  lastLogin: string;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const { showError } = useToast();
  
  const [accountType, setAccountType] = useState<AccountTypeData | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState<'personal' | 'security' | 'stats'>('personal');

  // Redirect se non autenticato
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin');
  }

  // Fetch dati account type e statistiche
  useEffect(() => {
    let isMounted = true;

    const fetchProfileData = async () => {
      if (!session?.user) return;

      try {
        if (isMounted) setIsLoading(true);

        // Fetch account type
        const accountResponse = await fetch('/api/user/account-type');
        if (!accountResponse.ok) {
          throw new Error('Errore nel caricamento tipo account');
        }

        if (isMounted) {
          const accountData = await accountResponse.json();
          setAccountType(accountData);

          // Fetch user stats (simulati per ora)
          const statsData: UserStats = {
            tripsCount: 0, // TODO: implementare API per statistiche reali
            joinDate: new Date().toLocaleDateString('it-IT'),
            lastLogin: new Date().toLocaleDateString('it-IT')
          };
          setUserStats(statsData);
        }
      } catch (error) {
        if (isMounted) {
          console.error('Errore nel caricamento dati profilo:', error);
          showError('Errore nel caricamento dei dati del profilo');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchProfileData();

    return () => {
      isMounted = false;
    };
  }, [session?.user?.id, showError]); // Dipendenza dall'ID utente e showError

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case UserRole.Explorer:
        return 'bg-green-100 text-green-800';
      case UserRole.Ranger:
        return 'bg-blue-100 text-blue-800';
      case UserRole.Sentinel:
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProviderDisplayName = (provider: string) => {
    switch (provider) {
      case 'google':
        return 'Google';
      case 'apple':
        return 'Apple';
      default:
        return provider.charAt(0).toUpperCase() + provider.slice(1);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm mb-8">
          <div className="px-6 py-8">
            <div className="flex items-center space-x-6">
              {session?.user?.image && (
                <Image
                  src={session.user.image}
                  alt={session.user.name || 'User'}
                  width={80}
                  height={80}
                  className="rounded-full"
                />
              )}
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900">
                  {session?.user?.name || 'Utente'}
                </h1>
                <p className="text-gray-600 mt-1">{session?.user?.email}</p>
                <div className="flex items-center gap-3 mt-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getRoleBadgeColor(session?.user?.role as UserRole)}`}>
                    {UserRoleLabels[session?.user?.role as UserRole]}
                  </span>
                  {accountType?.emailVerified && (
                    <span className="inline-flex items-center text-sm text-green-600">
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Email verificata
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  {UserRoleDescriptions[session?.user?.role as UserRole]}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'personal', name: 'Informazioni Personali', icon: User },
                { id: 'security', name: 'Sicurezza', icon: Shield },
                { id: 'stats', name: 'Statistiche', icon: Settings }
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveSection(tab.id as any)}
                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeSection === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-2" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content Sections */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-6 py-8">
            {activeSection === 'personal' && (
              <PersonalInfoSection session={session} accountType={accountType} />
            )}
            {activeSection === 'security' && (
              <SecuritySection accountType={accountType} />
            )}
            {activeSection === 'stats' && (
              <StatsSection userStats={userStats} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Componenti delle sezioni
function PersonalInfoSection({ session, accountType }: { session: any, accountType: AccountTypeData | null }) {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">Informazioni Personali</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nome Completo
          </label>
          <div className="flex items-center p-3 border border-gray-300 rounded-md bg-gray-50">
            <User className="w-5 h-5 text-gray-400 mr-3" />
            <span className="text-gray-900">{session?.user?.name || 'Non specificato'}</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email
          </label>
          <div className="flex items-center p-3 border border-gray-300 rounded-md bg-gray-50">
            <Mail className="w-5 h-5 text-gray-400 mr-3" />
            <span className="text-gray-900">{session?.user?.email}</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ruolo
          </label>
          <div className="flex items-center p-3 border border-gray-300 rounded-md bg-gray-50">
            <Shield className="w-5 h-5 text-gray-400 mr-3" />
            <span className="text-gray-900">{UserRoleLabels[session?.user?.role as UserRole]}</span>
          </div>
        </div>

        {accountType && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipo Account
            </label>
            <div className="flex items-center p-3 border border-gray-300 rounded-md bg-gray-50">
              <Settings className="w-5 h-5 text-gray-400 mr-3" />
              <span className="text-gray-900">
                {accountType.accountType === 'oauth' ? 'OAuth' : 
                 accountType.accountType === 'credentials' ? 'Credenziali' : 'Misto'}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function SecuritySection({ accountType }: { accountType: AccountTypeData | null }) {
  return (
    <div className="space-y-8">
      <h2 className="text-xl font-semibold text-gray-900">Sicurezza Account</h2>
      
      {/* Provider OAuth */}
      {accountType?.providers && accountType.providers.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Provider Collegati</h3>
          <div className="space-y-3">
            {accountType.providers.map((provider) => (
              <div key={provider} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-blue-600 font-semibold text-sm">
                      {getProviderDisplayName(provider).charAt(0)}
                    </span>
                  </div>
                  <span className="font-medium text-gray-900">{getProviderDisplayName(provider)}</span>
                </div>
                <span className="text-sm text-green-600 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Collegato
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Sezione Cambio Password */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Gestione Password</h3>
        <ChangePasswordForm accountTypeData={accountType} />
      </div>
    </div>
  );
}

function StatsSection({ userStats }: { userStats: UserStats | null }) {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">Statistiche Account</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-blue-50 p-6 rounded-lg">
          <div className="flex items-center">
            <MapPin className="w-8 h-8 text-blue-600 mr-3" />
            <div>
              <p className="text-2xl font-bold text-blue-900">{userStats?.tripsCount || 0}</p>
              <p className="text-blue-700">Viaggi Creati</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-lg">
          <div className="flex items-center">
            <Calendar className="w-8 h-8 text-green-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-green-900">{userStats?.joinDate}</p>
              <p className="text-green-700">Data Registrazione</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 p-6 rounded-lg">
          <div className="flex items-center">
            <User className="w-8 h-8 text-purple-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-purple-900">{userStats?.lastLogin}</p>
              <p className="text-purple-700">Ultimo Accesso</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function getProviderDisplayName(provider: string) {
  switch (provider) {
    case 'google':
      return 'Google';
    case 'apple':
      return 'Apple';
    default:
      return provider.charAt(0).toUpperCase() + provider.slice(1);
  }
}
