import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { z } from 'zod';
import { passwordSchema } from '@/lib/password-validation';

// Schema di validazione per la richiesta di cambio password
const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Password attuale richiesta'),
  newPassword: passwordSchema,
});

export async function POST(request: NextRequest) {
  try {
    // Verifica autenticazione
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }

    // Parse e validazione del body
    const body = await request.json();
    const result = changePasswordSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Dati non validi', 
          details: result.error.flatten().fieldErrors 
        },
        { status: 400 }
      );
    }

    const { currentPassword, newPassword } = result.data;

    // Recupera l'utente dal database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        password: true,
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Utente non trovato' },
        { status: 404 }
      );
    }

    // Verifica che l'utente abbia una password (non sia OAuth)
    if (!user.password) {
      return NextResponse.json(
        { error: 'Cambio password non disponibile per account OAuth' },
        { status: 400 }
      );
    }

    // Verifica password attuale
    const currentPasswordMatch = await bcrypt.compare(currentPassword, user.password);
    
    if (!currentPasswordMatch) {
      return NextResponse.json(
        { error: 'Password attuale non corretta' },
        { status: 400 }
      );
    }

    // Verifica che la nuova password sia diversa da quella attuale
    const isSamePassword = await bcrypt.compare(newPassword, user.password);
    
    if (isSamePassword) {
      return NextResponse.json(
        { error: 'La nuova password deve essere diversa da quella attuale' },
        { status: 400 }
      );
    }

    // Hash della nuova password
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    // Aggiorna la password nel database
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedNewPassword }
    });

    return NextResponse.json(
      { 
        success: true,
        message: 'Password aggiornata con successo!' 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Errore durante il cambio password:', error);
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}
