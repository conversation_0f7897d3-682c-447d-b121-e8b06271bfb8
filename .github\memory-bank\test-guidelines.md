# Testing guidelines

When implementing automated tests (unit, integration, e2e tests), never modify the actual project source code. When responding to a prompt, either you modify the tests or the project source code.
You don't need to get all the test passing, if you believe that the tests are correct and the project is not working, just stop the implementation and include in the answer a report on the failing tests and why they are failing, without actually make any modification to the project source code to make the test pass.