'use client';

import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import { useState, useEffect } from 'react';
import ChangePasswordForm from '@/components/ChangePasswordForm';

/**
 * Pagina di test semplificata per verificare che il loop infinito sia risolto
 */

interface AccountTypeData {
  accountType: 'oauth' | 'credentials' | 'mixed';
  isOAuthUser: boolean;
  hasPassword: boolean;
  canChangePassword: boolean;
  providers: string[];
  primaryProvider: string | null;
  emailVerified: boolean;
}

export default function ProfileTestPage() {
  const { data: session, status } = useSession();
  const [accountType, setAccountType] = useState<AccountTypeData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiCallCount, setApiCallCount] = useState(0);

  // Redirect se non autenticato
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin');
  }

  // Fetch dati account type UNA SOLA VOLTA
  useEffect(() => {
    let isMounted = true;
    
    const fetchAccountType = async () => {
      if (!session?.user || accountType) return;
      
      try {
        setIsLoading(true);
        setApiCallCount(prev => prev + 1);
        
        console.log(`🔍 Chiamata API #${apiCallCount + 1} - account-type`);
        
        const response = await fetch('/api/user/account-type');
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        if (isMounted) {
          const data = await response.json();
          console.log('✅ Risposta API:', data);
          setAccountType(data);
        }
        
      } catch (err) {
        if (isMounted) {
          const errorMessage = err instanceof Error ? err.message : 'Errore sconosciuto';
          console.error('❌ Errore API:', errorMessage);
          setError(errorMessage);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchAccountType();
    
    return () => {
      isMounted = false;
    };
  }, [session?.user?.id]); // Solo dipendenza dall'ID utente

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm mb-8 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            🧪 Profile Test - Loop Fix
          </h1>
          <p className="text-gray-600">
            Test per verificare che il loop infinito sia risolto
          </p>
          
          <div className="mt-4 p-4 bg-gray-100 rounded-lg">
            <p className="text-sm">
              <strong>Chiamate API effettuate:</strong> {apiCallCount}
            </p>
            <p className="text-sm text-gray-600">
              Questo numero dovrebbe rimanere a 1 dopo il caricamento iniziale
            </p>
          </div>
        </div>

        {/* Stato caricamento */}
        {isLoading && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
              <span className="text-blue-800">Caricamento dati account...</span>
            </div>
          </div>
        )}

        {/* Errori */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-red-900 mb-2">❌ Errore</h2>
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Informazioni account */}
        {accountType && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-green-900 mb-4">✅ Account Type</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>Tipo:</strong> {accountType.accountType}</p>
                <p><strong>È OAuth:</strong> {accountType.isOAuthUser ? 'Sì' : 'No'}</p>
                <p><strong>Ha Password:</strong> {accountType.hasPassword ? 'Sì' : 'No'}</p>
              </div>
              <div>
                <p><strong>Può cambiare password:</strong> {accountType.canChangePassword ? 'Sì' : 'No'}</p>
                <p><strong>Provider:</strong> {accountType.providers.join(', ') || 'Nessuno'}</p>
                <p><strong>Email verificata:</strong> {accountType.emailVerified ? 'Sì' : 'No'}</p>
              </div>
            </div>
          </div>
        )}

        {/* Form cambio password */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Form Cambio Password</h2>
          <p className="text-sm text-gray-600 mb-6">
            Il form dovrebbe ricevere i dati come prop e NON fare chiamate API aggiuntive
          </p>
          
          <ChangePasswordForm accountTypeData={accountType} />
        </div>

        {/* Istruzioni */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-6">
          <h2 className="text-lg font-semibold text-yellow-900 mb-2">📝 Come verificare il fix</h2>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>1. Apri la console del browser (F12)</li>
            <li>2. Monitora i log delle chiamate API</li>
            <li>3. Il contatore "Chiamate API" dovrebbe rimanere a 1</li>
            <li>4. Non dovrebbero esserci query Prisma ripetute nel log del server</li>
            <li>5. Il form dovrebbe funzionare normalmente</li>
          </ul>
        </div>

      </div>
    </div>
  );
}
