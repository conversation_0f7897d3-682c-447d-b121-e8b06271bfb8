# Copilot Custom Instructions

- Prima di proseguire con le implementazioni, fai sempre un piano di sviluppo e fallo approvare da me
- Se ti mancano delle informazioni, chiedimele senza proseguire e fare supposizioni
- Quando trovi discrepanze tra test e implementazione, fermati e chiedimi sempre quale approccio preferisco (modificare test vs implementazione) prima di procedere
- Implementa solo quello che ti viene richiesto, senza aggiungere funzionalità extra
- Evita sempre di fare test e implementazioni nello stesso momento: se stai modificando i file del progetto, non modificare i file di test e viceversa
- Prima di modificare qualsiasi file (test o implementazione), presenta sempre le opzioni disponibili e aspetta la mia approvazione
- Consulta gli MCP server confiugurati per richiedere ulteriori informazioni
- Consulta sempre i file nella cartella `.github/memory-bank/` prima di generare codice per mantenere coerenza con le scelte di progetto.
- Aggiorna sempre il file `requirements.md` con le novità più rilevanti sulle funzionalità del progetto.