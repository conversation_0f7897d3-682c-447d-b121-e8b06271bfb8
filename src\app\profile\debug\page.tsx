'use client';

import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import { useState, useEffect } from 'react';

/**
 * Pagina di debug semplificata per testare l'API account-type
 * senza l'interfaccia complessa del profilo
 */

interface AccountTypeData {
  accountType: 'oauth' | 'credentials' | 'mixed';
  isOAuthUser: boolean;
  hasPassword: boolean;
  canChangePassword: boolean;
  providers: string[];
  primaryProvider: string | null;
  emailVerified: boolean;
}

export default function ProfileDebugPage() {
  const { data: session, status } = useSession();
  const [accountType, setAccountType] = useState<AccountTypeData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [callCount, setCallCount] = useState(0);

  // Redirect se non autenticato
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin');
  }

  // Fetch manuale per test
  const fetchAccountType = async () => {
    if (!session?.user) return;
    
    setIsLoading(true);
    setError(null);
    setCallCount(prev => prev + 1);
    
    try {
      console.log(`🔍 Chiamata API #${callCount + 1} - account-type`);
      
      const response = await fetch('/api/user/account-type');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ Risposta API:', data);
      
      setAccountType(data);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore sconosciuto';
      console.error('❌ Errore API:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-fetch al mount (una sola volta)
  useEffect(() => {
    let isMounted = true;
    
    const autoFetch = async () => {
      if (!session?.user || accountType) return;
      
      console.log('🚀 Auto-fetch al mount');
      await fetchAccountType();
    };
    
    if (isMounted) {
      autoFetch();
    }
    
    return () => {
      isMounted = false;
    };
  }, [session?.user?.id]); // Solo dipendenza dall'ID utente

  const resetTest = () => {
    setAccountType(null);
    setError(null);
    setCallCount(0);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm mb-8 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            🧪 Profile API Debug Page
          </h1>
          <p className="text-gray-600">
            Pagina di debug per testare l'API account-type senza loop infiniti
          </p>
        </div>

        {/* Informazioni sessione */}
        <div className="bg-blue-50 rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-blue-900 mb-3">Sessione Corrente</h2>
          <div className="space-y-2 text-sm">
            <p><strong>Status:</strong> {status}</p>
            <p><strong>User ID:</strong> {session?.user?.id}</p>
            <p><strong>Email:</strong> {session?.user?.email}</p>
            <p><strong>Nome:</strong> {session?.user?.name}</p>
            <p><strong>Ruolo:</strong> {session?.user?.role}</p>
          </div>
        </div>

        {/* Controlli */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Controlli Test</h2>
          <div className="space-x-4">
            <button
              onClick={fetchAccountType}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Caricamento...' : 'Fetch Account Type'}
            </button>
            
            <button
              onClick={resetTest}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Reset Test
            </button>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Chiamate API effettuate:</strong> {callCount}</p>
          </div>
        </div>

        {/* Errori */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-red-900 mb-2">❌ Errore</h2>
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Risultati */}
        {accountType && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-green-900 mb-4">✅ Risultati API</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <h3 className="font-medium text-green-800 mb-2">Informazioni Base</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li><strong>Tipo Account:</strong> {accountType.accountType}</li>
                  <li><strong>È OAuth:</strong> {accountType.isOAuthUser ? 'Sì' : 'No'}</li>
                  <li><strong>Ha Password:</strong> {accountType.hasPassword ? 'Sì' : 'No'}</li>
                  <li><strong>Può cambiare password:</strong> {accountType.canChangePassword ? 'Sì' : 'No'}</li>
                  <li><strong>Email verificata:</strong> {accountType.emailVerified ? 'Sì' : 'No'}</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium text-green-800 mb-2">Provider OAuth</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li><strong>Provider primario:</strong> {accountType.primaryProvider || 'Nessuno'}</li>
                  <li><strong>Tutti i provider:</strong> {accountType.providers.length > 0 ? accountType.providers.join(', ') : 'Nessuno'}</li>
                </ul>
              </div>
            </div>
            
            <details className="mt-4">
              <summary className="cursor-pointer font-medium text-green-800">
                📋 Dati JSON completi
              </summary>
              <pre className="mt-2 text-xs text-green-700 bg-green-100 p-3 rounded overflow-x-auto">
                {JSON.stringify(accountType, null, 2)}
              </pre>
            </details>
          </div>
        )}

        {/* Istruzioni */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-yellow-900 mb-2">📝 Istruzioni</h2>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• Questa pagina effettua una sola chiamata API automatica al mount</li>
            <li>• Usa il pulsante "Fetch Account Type" per chiamate manuali</li>
            <li>• Monitora la console del browser per i log dettagliati</li>
            <li>• Controlla il contatore delle chiamate API per verificare l'assenza di loop</li>
            <li>• Usa "Reset Test" per pulire i risultati</li>
          </ul>
        </div>

      </div>
    </div>
  );
}
