'use client';

import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import { useState, useEffect } from 'react';

/**
 * Versione MINIMA della pagina profilo per identificare la causa del loop infinito
 * Questa versione NON usa:
 * - useToast
 * - Componenti complessi
 * - ChangePasswordForm
 * - Dipendenze esterne
 */

interface AccountTypeData {
  accountType: 'oauth' | 'credentials' | 'mixed';
  isOAuthUser: boolean;
  hasPassword: boolean;
  canChangePassword: boolean;
  providers: string[];
  primaryProvider: string | null;
  emailVerified: boolean;
}

export default function MinimalProfilePage() {
  const { data: session, status } = useSession();
  
  const [accountType, setAccountType] = useState<AccountTypeData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fetchCount, setFetchCount] = useState(0);

  // Redirect se non autenticato
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    redirect('/auth/signin');
  }

  // Fetch dati account type - VERSIONE MINIMA
  useEffect(() => {
    let isMounted = true;
    
    const fetchAccountType = async () => {
      if (!session?.user?.id) {
        console.log('❌ Nessun user ID nella sessione');
        return;
      }
      
      if (accountType) {
        console.log('✅ Dati già presenti, skip fetch');
        return;
      }
      
      try {
        setFetchCount(prev => {
          const newCount = prev + 1;
          console.log(`🔍 Fetch #${newCount} - account-type per user: ${session.user.id}`);
          return newCount;
        });
        
        setIsLoading(true);
        
        const response = await fetch('/api/user/account-type');
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        if (isMounted) {
          const data = await response.json();
          console.log('✅ Dati ricevuti:', data);
          setAccountType(data);
          setError(null);
        }
        
      } catch (err) {
        if (isMounted) {
          const errorMessage = err instanceof Error ? err.message : 'Errore sconosciuto';
          console.error('❌ Errore fetch:', errorMessage);
          setError(errorMessage);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    console.log('🚀 useEffect triggered - session.user.id:', session?.user?.id);
    fetchAccountType();
    
    return () => {
      console.log('🧹 Cleanup useEffect');
      isMounted = false;
    };
  }, [session?.user?.id]); // SOLO dipendenza dall'ID utente

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm mb-8 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            🔬 Minimal Profile Page
          </h1>
          <p className="text-gray-600 mb-4">
            Versione minima per identificare la causa del loop infinito
          </p>
          
          {/* Debug Info */}
          <div className="bg-gray-100 p-4 rounded-lg text-sm">
            <p><strong>Fetch Count:</strong> {fetchCount}</p>
            <p><strong>Session Status:</strong> {status}</p>
            <p><strong>User ID:</strong> {session?.user?.id}</p>
            <p><strong>User Email:</strong> {session?.user?.email}</p>
            <p><strong>Loading:</strong> {isLoading ? 'Sì' : 'No'}</p>
            <p><strong>Has Data:</strong> {accountType ? 'Sì' : 'No'}</p>
          </div>
        </div>

        {/* Loading */}
        {isLoading && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
              <span className="text-blue-800">Caricamento dati account...</span>
            </div>
          </div>
        )}

        {/* Error */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-red-900 mb-2">❌ Errore</h2>
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Success */}
        {accountType && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-green-900 mb-4">✅ Dati Account</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-green-800 mb-2">Informazioni Base</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li><strong>Tipo Account:</strong> {accountType.accountType}</li>
                  <li><strong>È OAuth:</strong> {accountType.isOAuthUser ? 'Sì' : 'No'}</li>
                  <li><strong>Ha Password:</strong> {accountType.hasPassword ? 'Sì' : 'No'}</li>
                  <li><strong>Può cambiare password:</strong> {accountType.canChangePassword ? 'Sì' : 'No'}</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium text-green-800 mb-2">Provider OAuth</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li><strong>Provider primario:</strong> {accountType.primaryProvider || 'Nessuno'}</li>
                  <li><strong>Tutti i provider:</strong> {accountType.providers.length > 0 ? accountType.providers.join(', ') : 'Nessuno'}</li>
                  <li><strong>Email verificata:</strong> {accountType.emailVerified ? 'Sì' : 'No'}</li>
                </ul>
              </div>
            </div>
            
            <details className="mt-4">
              <summary className="cursor-pointer font-medium text-green-800">
                📋 JSON completo
              </summary>
              <pre className="mt-2 text-xs text-green-700 bg-green-100 p-3 rounded overflow-x-auto">
                {JSON.stringify(accountType, null, 2)}
              </pre>
            </details>
          </div>
        )}

        {/* Istruzioni */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-yellow-900 mb-2">📝 Test Loop Infinito</h2>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• <strong>Fetch Count</strong> dovrebbe rimanere a 1 dopo il caricamento</li>
            <li>• Controlla la console del browser per i log dettagliati</li>
            <li>• Monitora il log del server per query Prisma ripetute</li>
            <li>• Se il fetch count continua ad aumentare, il problema è nel useEffect</li>
            <li>• Se rimane a 1 ma le query Prisma continuano, il problema è nell'API</li>
          </ul>
          
          <div className="mt-4 p-3 bg-yellow-100 rounded">
            <p className="text-xs text-yellow-700">
              <strong>Questa pagina NON usa:</strong> useToast, ChangePasswordForm, componenti complessi
            </p>
          </div>
        </div>

      </div>
    </div>
  );
}
