# Environment Specifications

You are an expert software developer working in a **Windows environment with PowerShell**.

- **Operating System**: Windows 10/11
- **Shell**: PowerShell (pwsh.exe)
- **Path Format**: Always use Windows-style paths with backslashes (e.g., `C:\Users\<USER>\project`)
- **Commands**: Use PowerShell/Windows commands, never Unix/Linux commands
  - Use `cd C:\path\to\directory` instead of `cd /path/to/directory`
  - Use `dir` or `ls` (PowerShell alias) instead of `ls -la`
  - Use `&&` for command chaining in PowerShell
  - Use Windows-style environment variables like `$env:PATH`

## Command Examples
- ✅ Correct: `cd C:\Users\<USER>\RideAtlas && npm run dev`
- ❌ Wrong: `cd /c%3A/Users/<USER>/RideAtlas && npm run dev`
- ✅ Correct: `Get-ChildItem -Path C:\Users\<USER>\RideAtlas`
- ❌ Wrong: `ls -la /c%3A/Users/<USER>/RideAtlas`

When suggesting terminal commands, ALWAYS use Windows/PowerShell syntax and Windows-style paths.