/**
 * Script di test per verificare il funzionamento delle API del profilo
 * 
 * Questo script testa:
 * 1. API /api/user/account-type
 * 2. Logica di rilevamento OAuth vs Credentials
 * 3. Integrazione con il database
 */

import { prisma } from '@/lib/prisma';

async function testAccountTypeDetection() {
  console.log('🧪 Test Account Type Detection API');
  console.log('=====================================\n');

  try {
    // Test 1: Verifica utenti nel database
    console.log('📊 Analisi utenti nel database:');
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        password: true,
        accounts: {
          select: {
            provider: true,
            type: true,
          }
        }
      },
      take: 5 // Limita a 5 utenti per il test
    });

    console.log(`Trovati ${users.length} utenti:\n`);

    users.forEach((user, index) => {
      const hasPassword = user.password !== null;
      const oauthProviders = user.accounts
        .filter(account => account.type === 'oauth')
        .map(account => account.provider);
      
      let accountType: string;
      if (oauthProviders.length > 0 && !hasPassword) {
        accountType = 'OAuth puro';
      } else if (oauthProviders.length === 0 && hasPassword) {
        accountType = 'Credentials puro';
      } else if (oauthProviders.length > 0 && hasPassword) {
        accountType = 'Misto';
      } else {
        accountType = 'Indefinito';
      }

      console.log(`${index + 1}. ${user.email}`);
      console.log(`   - Tipo: ${accountType}`);
      console.log(`   - Password: ${hasPassword ? '✅ Presente' : '❌ Assente'}`);
      console.log(`   - Provider OAuth: ${oauthProviders.length > 0 ? oauthProviders.join(', ') : 'Nessuno'}`);
      console.log('');
    });

    // Test 2: Simulazione logica API
    console.log('🔍 Test logica di determinazione tipo account:');
    console.log('');

    const testCases = [
      {
        name: 'Utente OAuth Google',
        hasPassword: false,
        providers: ['google'],
        expected: { accountType: 'oauth', canChangePassword: false }
      },
      {
        name: 'Utente Credentials',
        hasPassword: true,
        providers: [],
        expected: { accountType: 'credentials', canChangePassword: true }
      },
      {
        name: 'Utente Misto',
        hasPassword: true,
        providers: ['google'],
        expected: { accountType: 'mixed', canChangePassword: true }
      }
    ];

    testCases.forEach((testCase, index) => {
      const { hasPassword, providers } = testCase;
      const hasOAuthProviders = providers.length > 0;

      let accountType: 'oauth' | 'credentials' | 'mixed';
      let canChangePassword: boolean;

      if (hasOAuthProviders && !hasPassword) {
        accountType = 'oauth';
        canChangePassword = false;
      } else if (!hasOAuthProviders && hasPassword) {
        accountType = 'credentials';
        canChangePassword = true;
      } else if (hasOAuthProviders && hasPassword) {
        accountType = 'mixed';
        canChangePassword = true;
      } else {
        accountType = 'credentials';
        canChangePassword = false;
      }

      const result = { accountType, canChangePassword };
      const isCorrect = 
        result.accountType === testCase.expected.accountType &&
        result.canChangePassword === testCase.expected.canChangePassword;

      console.log(`${index + 1}. ${testCase.name}`);
      console.log(`   Input: password=${hasPassword}, providers=[${providers.join(', ')}]`);
      console.log(`   Output: ${JSON.stringify(result)}`);
      console.log(`   Expected: ${JSON.stringify(testCase.expected)}`);
      console.log(`   Result: ${isCorrect ? '✅ PASS' : '❌ FAIL'}`);
      console.log('');
    });

    // Test 3: Verifica struttura database
    console.log('🗄️  Verifica struttura database:');
    console.log('');

    const accountsCount = await prisma.account.count();
    const usersWithPassword = await prisma.user.count({
      where: { password: { not: null } }
    });
    const usersWithoutPassword = await prisma.user.count({
      where: { password: null }
    });

    console.log(`- Account OAuth totali: ${accountsCount}`);
    console.log(`- Utenti con password: ${usersWithPassword}`);
    console.log(`- Utenti senza password: ${usersWithoutPassword}`);
    console.log('');

    // Test 4: Query di esempio per l'API
    console.log('📝 Query di esempio per API:');
    console.log('');

    if (users.length > 0) {
      const sampleUser = users[0];
      
      const userWithAccounts = await prisma.user.findUnique({
        where: { id: sampleUser.id },
        select: {
          id: true,
          email: true,
          password: true,
          emailVerified: true,
          accounts: {
            select: {
              provider: true,
              type: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'asc'
            }
          }
        }
      });

      console.log('Query risultato per utente campione:');
      console.log(JSON.stringify(userWithAccounts, null, 2));
    }

    console.log('\n✅ Test completato con successo!');

  } catch (error) {
    console.error('❌ Errore durante il test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Esegui il test se lo script viene chiamato direttamente
if (require.main === module) {
  testAccountTypeDetection();
}

export { testAccountTypeDetection };
