# RideAtlas - Technology Stack Summary

## Overview

RideAtlas è una piattaforma web per la condivisione di viaggi in moto, sviluppata seguendo principi di Clean Code, Clean Architecture e Design Patterns.

---

## Frontend Stack

### Core Framework

- **Next.js 14.2.28** - React framework con App Router per SSR/SSG
- **React 18.2.0** - Libreria UI declarativa
- **TypeScript 5.3.3** - Tipizzazione statica per JavaScript

### Styling & UI

- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Componenti UI accessibili
  
---

## Backend Stack

### Runtime & Framework

- **Node.js** - Runtime JavaScript server-side
- **Next.js API Routes** - Endpoints API integrati

### Database & ORM

- **PostgreSQL** - Database relazionale principale
- **Prisma** - ORM type-safe per TypeScript
- **Prisma Client** - Client database generato automaticamente

### Authentication & Security

- **NextAuth.js** - Sistema di autenticazione
- **Google OAuth Provider** - Autenticazione tramite Google
- **Credentials Provider** - Autenticazione email/password
- **Zod** - Validazione e parsing dei dati

### Email & Communication

- **Nodemailer** - Invio email SMTP
- **Email Templates** - Template per verifica email e notifiche

---

## Development & Testing

### Testing Framework

- **Jest** - Framework di testing JavaScript
- **React Testing Library** - Testing componenti React

### Development Tools

- **ESLint** - Linting JavaScript/TypeScript

### Build & Deployment

- **Vercel** - Piattaforma di deployment
- **Next.js Build** - Compilazione produzione

---
