'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

/**
 * Componente di test per verificare l'integrazione delle API del profilo
 * 
 * Questo componente può essere temporaneamente aggiunto a una pagina
 * per testare il funzionamento delle API senza dover navigare nell'interfaccia
 */

interface AccountTypeData {
  accountType: 'oauth' | 'credentials' | 'mixed';
  isOAuthUser: boolean;
  hasPassword: boolean;
  canChangePassword: boolean;
  providers: string[];
  primaryProvider: string | null;
  emailVerified: boolean;
  metadata: {
    totalProviders: number;
    registrationMethod: string;
    securityLevel: 'high' | 'standard';
  };
}

export default function ProfileTestComponent() {
  const { data: session, status } = useSession();
  const [accountType, setAccountType] = useState<AccountTypeData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<any[]>([]);

  // Test API account-type
  const testAccountTypeAPI = async () => {
    if (!session?.user) {
      setError('Utente non autenticato');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/user/account-type');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setAccountType(data);
      
      // Aggiungi risultato ai test
      setTestResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        test: 'Account Type API',
        status: 'SUCCESS',
        data: data
      }]);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore sconosciuto';
      setError(errorMessage);
      
      setTestResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        test: 'Account Type API',
        status: 'ERROR',
        error: errorMessage
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // Test API change-password (solo validazione, non cambio effettivo)
  const testChangePasswordAPI = async () => {
    if (!session?.user) {
      setError('Utente non autenticato');
      return;
    }

    try {
      const response = await fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: 'test-password',
          newPassword: 'new-test-password'
        }),
      });

      const data = await response.json();
      
      setTestResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        test: 'Change Password API (validation)',
        status: response.ok ? 'SUCCESS' : 'EXPECTED_ERROR',
        data: data,
        note: 'Test di validazione - password non cambiate'
      }]);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore sconosciuto';
      
      setTestResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        test: 'Change Password API',
        status: 'ERROR',
        error: errorMessage
      }]);
    }
  };

  // Auto-test al mount se autenticato
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      testAccountTypeAPI();
    }
  }, [status, session]);

  const clearResults = () => {
    setTestResults([]);
    setError(null);
    setAccountType(null);
  };

  if (status === 'loading') {
    return (
      <div className="bg-gray-100 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">🧪 Profile API Test Component</h3>
        <p>Caricamento sessione...</p>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="bg-yellow-100 border border-yellow-400 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">🧪 Profile API Test Component</h3>
        <p className="text-yellow-800">⚠️ Utente non autenticato. Effettua il login per testare le API.</p>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-4">🧪 Profile API Test Component</h3>
      
      {/* Informazioni sessione */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Sessione Corrente</h4>
        <p className="text-sm text-blue-800">
          <strong>Email:</strong> {session?.user?.email}<br/>
          <strong>Nome:</strong> {session?.user?.name}<br/>
          <strong>Ruolo:</strong> {session?.user?.role}
        </p>
      </div>

      {/* Controlli test */}
      <div className="mb-6 space-x-4">
        <button
          onClick={testAccountTypeAPI}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Test Account Type API'}
        </button>
        
        <button
          onClick={testChangePasswordAPI}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          Test Change Password API
        </button>
        
        <button
          onClick={clearResults}
          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
        >
          Clear Results
        </button>
      </div>

      {/* Errori */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-medium text-red-900 mb-2">❌ Errore</h4>
          <p className="text-sm text-red-800">{error}</p>
        </div>
      )}

      {/* Risultati Account Type */}
      {accountType && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-medium text-green-900 mb-2">✅ Account Type Data</h4>
          <pre className="text-sm text-green-800 overflow-x-auto">
            {JSON.stringify(accountType, null, 2)}
          </pre>
        </div>
      )}

      {/* Log dei test */}
      {testResults.length > 0 && (
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">📋 Test Results Log</h4>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm p-2 bg-white rounded border">
                <div className="flex justify-between items-start mb-1">
                  <span className="font-medium">{result.test}</span>
                  <span className={`px-2 py-1 rounded text-xs ${
                    result.status === 'SUCCESS' ? 'bg-green-100 text-green-800' :
                    result.status === 'EXPECTED_ERROR' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {result.status}
                  </span>
                </div>
                <p className="text-gray-600 text-xs mb-1">{result.timestamp}</p>
                {result.data && (
                  <pre className="text-xs text-gray-700 overflow-x-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                )}
                {result.error && (
                  <p className="text-xs text-red-600">{result.error}</p>
                )}
                {result.note && (
                  <p className="text-xs text-blue-600 italic">{result.note}</p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
